/**
 * ScanOnWeb - 扫描控件 JavaScript SDK
 * TypeScript 定义文件
 */

/**
 * 扫描配置参数接口
 */
export interface ScanConfig {
  /** 是否显示扫描控件工作界面 */
  showUI: boolean;
  /** DPI 分辨率 X */
  dpi_x: number;
  /** DPI 分辨率 Y */
  dpi_y: number;
  /** 选中的扫描仪硬件设备id索引 */
  deviceIndex: number;
  /** 是否显示设备内置对话框 */
  showDialog: boolean;
  /** 是否使用自动进纸器 */
  autoFeedEnable: boolean;
  /** 是否自动装填纸张 */
  autoFeed: boolean;
  /** 是否使用双面扫描模式 */
  dupxMode: boolean;
  /** 是否使用自动纠偏模式 */
  autoDeskew: boolean;
  /** 是否使用自动边框检测 */
  autoBorderDetection: boolean;
  /** 色彩模式 */
  colorMode: 'RGB' | 'BW' | 'GRAY';
  /** 数据传输模式 */
  transMode: 'memory' | 'file' | 'native';
}

/**
 * WebSocket 消息接口
 */
export interface WebSocketMessage {
  /** 命令类型 */
  cmd_type: string;
  /** 图像数量 */
  imageCount?: number;
  /** 当前设备索引 */
  currentIndex?: number;
  /** 其他属性 */
  [key: string]: any;
}

/**
 * 扫描控件类
 */
export default class ScanOnWeb {
  /** 扫描配置参数 */
  scaner_work_config: ScanConfig;
  
  /** WebSocket 连接对象 */
  h5socket: WebSocket | null;
  
  /** 扫描结果图像总数 */
  imageCount: number;

  // 事件回调函数（可选）
  onGetDevicesListEvent?: (msg: WebSocketMessage) => void;
  onScanFinishedEvent?: (msg: WebSocketMessage) => void;
  onSelectScanDeviceEvent?: (msg: WebSocketMessage) => void;
  onGetImageCountEvent?: (msg: WebSocketMessage) => void;
  onGetAllImageEvent?: (msg: WebSocketMessage) => void;
  onGetImageByIdEvent?: (msg: WebSocketMessage) => void;
  onLoadImageFromUrlEvent?: (msg: WebSocketMessage) => void;
  onRotateImageEvent?: (msg: WebSocketMessage) => void;
  onGetImageSizeEvent?: (msg: WebSocketMessage) => void;
  onUploadAllImageAsPdfToUrlEvent?: (msg: WebSocketMessage) => void;
  onUploadAllImageAsTiffToUrlEvent?: (msg: WebSocketMessage) => void;
  onUploadJpgImageByIndexEvent?: (msg: WebSocketMessage) => void;
  onUploadEvent?: (msg: WebSocketMessage) => void;
  onImageEditedEvent?: (msg: WebSocketMessage) => void;
  onImageDrapEvent?: (msg: WebSocketMessage) => void;

  /**
   * 构造函数
   */
  constructor();

  /**
   * 通过连接多个websocket server端口返回一个可用的websocket连接对象
   * @param wssUrls WebSocket服务器URL数组
   * @returns WebSocket连接Promise
   */
  getConnectedServer(wssUrls: string[]): Promise<WebSocket>;

  /**
   * 尝试检测websocket哪个端口可以成功连接
   */
  tryConnect(): void;

  /**
   * 初始化websocket相关的函数绑定
   * @param server WebSocket服务器实例
   */
  initWebsocketCallback(server: WebSocket): void;

  /**
   * WebSocket错误处理
   * @param event 错误事件
   */
  onSocketError(event: Event): void;

  /**
   * 判断回调函数是否存在
   * @param f 要检查的函数
   * @returns 函数是否存在且为函数类型
   */
  isCallbackExist(f: any): f is Function;

  /**
   * WebSocket消息处理
   * @param event WebSocket消息事件
   */
  onSocketMessage(event: MessageEvent): void;

  /**
   * 通过websocket发送数据给webscoket服务端
   * @param commandData 要发送的命令数据
   */
  sendWebSocketCommand(commandData: object): void;

  /**
   * 设置授权信息
   * @param licenseMode 授权模式
   * @param key1 授权密钥1
   * @param key2 授权密钥2
   * @param licenseServerUrl 授权服务器URL
   */
  setLicenseKey(licenseMode: string, key1: string, key2: string, licenseServerUrl: string): void;

  /**
   * 加载所有可用的扫描设备
   */
  loadDevices(): void;

  /**
   * 设置当前选中的扫描设备id
   * @param deviceIndex 设备索引
   */
  selectScanDevice(deviceIndex: number): void;

  /**
   * 开始扫描
   */
  startScan(): void;

  /**
   * 清除全部扫描结果
   */
  clearAll(): void;

  /**
   * 获取图像总数
   */
  getImageCount(): void;

  /**
   * 获取所有图像
   */
  getAllImage(): void;

  /**
   * 发送指令获取某一页图像到托盘服务
   * @param index 图像索引
   */
  getImageById(index: number): void;

  /**
   * 发送指令远程加载服务器端的多页图像到托盘服务
   * @param url 图像URL
   */
  loadImageFromUrl(url: string): void;

  /**
   * 发送指令旋转某一页图像到托盘服务
   * @param index 图像索引
   * @param angle 旋转角度
   */
  rotateImage(index: number, angle: number): void;

  /**
   * 发送指令获取某一页图像的宽度到托盘服务
   * @param index 图像索引
   */
  getImageSize(index: number): void;

  /**
   * 发送指令删除某一页图像到托盘服务
   * @param index 图像索引
   */
  deleteImageByIndex(index: number): void;

  /**
   * 以pdf格式上传全部图像到服务器端
   * @param url 上传URL
   * @param id 标识ID
   * @param desc 描述信息
   */
  uploadAllImageAsPdfToUrl(url: string, id: string, desc: string): void;

  /**
   * 以tiff格式上传全部图像到服务器端
   * @param url 上传URL
   * @param id 标识ID
   * @param desc 描述信息
   */
  uploadAllImageAsTiffToUrl(url: string, id: string, desc: string): void;

  /**
   * 以jpg格式上传某一页图像到服务器端
   * @param url 上传URL
   * @param id 标识ID
   * @param desc 描述信息
   * @param index 图像索引
   */
  uploadJpgImageByIndex(url: string, id: string, desc: string, index: number): void;

  /**
   * 全部图像保存到客户端本地文件
   * @param filename 文件名
   */
  saveAllImageToLocal(filename: string): void;

  /**
   * 从客户端本地读取图像,通过打开文件对话框选择图像文件
   */
  openClientLocalfile(): void;

  /**
   * ftp上传全部图像文件到服务器端
   * @param serverIp 服务器IP
   * @param port 端口
   * @param username 用户名
   * @param password 密码
   * @param serverPath 服务器路径
   * @param filename 文件名
   */
  ftpUploadAllImage(serverIp: string, port: number, username: string, password: string, serverPath: string, filename: string): void;

  /**
   * 设置上传按钮是否可见
   * @param visible 是否可见
   */
  setUploadButtonVisible(visible: boolean): void;

  /**
   * 设置焦点
   */
  setFocus(): void;

  /**
   * 隐藏窗口
   */
  hidden(): void;

  /**
   * 关闭websocket连接
   */
  closeWebSocket(): void;
}
