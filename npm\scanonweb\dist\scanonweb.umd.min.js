/*!
 * scanonweb v1.0.1
 * ScanOnWeb - 扫描控件 JavaScript SDK，用于与本地扫描服务程序通信
 * https://www.brainysoft.cn
 * 
 * Copyright (c) 2025 BrainySoft
 * Licensed under the MIT license
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).ScanOnWeb=t()}(this,(function(){"use strict";class e{constructor(){this.scaner_work_config={showUI:!1,dpi_x:300,dpi_y:300,deviceIndex:0,showDialog:!1,autoFeedEnable:!0,autoFeed:!1,dupxMode:!1,autoDeskew:!1,autoBorderDetection:!1,colorMode:"RGB",transMode:"memory"},this.h5socket=null,this.imageCount=0,this.tryConnect()}getConnectedServer(e){return console.log("尝试连接托盘扫描服务websocket服务器..."),new Promise(((t,o)=>{const s=new WebSocket(e[0]);s.onopen=()=>{t(s)},s.onerror=e=>{o(e)}})).then((e=>(console.log("连接websocket服务器成功!"),this.initWebsocketCallback(e),console.log("尝试获取扫描设备列表..."),this.loadDevices(),e)),(t=>{if(e.length>1)return this.getConnectedServer(e.slice(1));throw t}))}tryConnect(){this.getConnectedServer(["ws://127.0.0.1:1001","ws://127.0.0.1:2001","ws://127.0.0.1:3001","ws://127.0.0.1:4001","ws://127.0.0.1:5001"])}initWebsocketCallback(e){this.h5socket=e,this.h5socket.onerror=this.onSocketError.bind(this),this.h5socket.onmessage=this.onSocketMessage.bind(this)}onSocketError(e){alert("无法连接扫描服务程序,请检查扫描服务程序是否已经启动！"),console.log("WebSocket error: "+e.data)}isCallbackExist(e){return!(!e||void 0===e||void 0===e)&&"function"==typeof e}onSocketMessage(e){const t=JSON.parse(e.data);switch(t.cmd_type){case"getDevicesList":this.isCallbackExist(this.onGetDevicesListEvent)&&this.onGetDevicesListEvent(t);break;case"scanComplete":this.imageCount=t.imageCount,this.isCallbackExist(this.onScanFinishedEvent)&&this.onScanFinishedEvent(t);break;case"selectScanDevice":this.scaner_work_config.deviceIndex=t.currentIndex,this.scaner_work_config.showDialog=t.showDialog,this.scaner_work_config.autoFeedEnable=t.autoFeedEnable,this.scaner_work_config.autoFeed=t.autoFeed,this.scaner_work_config.dupxMode=t.dupxMode,this.scaner_work_config.autoDeskew=t.autoDeskew,this.scaner_work_config.autoBorderDetection=t.autoBorderDetection,this.isCallbackExist(this.onSelectScanDeviceEvent)&&this.onSelectScanDeviceEvent(t);break;case"getImageCount":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetImageCountEvent)&&this.onGetImageCountEvent(t);break;case"getAllImage":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetAllImageEvent)&&this.onGetAllImageEvent(t);break;case"getImageById":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetImageByIdEvent)&&this.onGetImageByIdEvent(t);break;case"loadImageFromUrl":this.imageCount=t.imageCount,this.isCallbackExist(this.onLoadImageFromUrlEvent)&&this.onLoadImageFromUrlEvent(t);break;case"rotateImage":this.imageCount=t.imageCount,this.isCallbackExist(this.onRotateImageEvent)&&this.onRotateImageEvent(t);break;case"getImageSize":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetImageSizeEvent)&&this.onGetImageSizeEvent(t);break;case"uploadAllImageAsPdfToUrl":this.isCallbackExist(this.onUploadAllImageAsPdfToUrlEvent)&&this.onUploadAllImageAsPdfToUrlEvent(t);break;case"uploadAllImageAsTiffToUrl":this.isCallbackExist(this.onUploadAllImageAsTiffToUrlEvent)&&this.onUploadAllImageAsTiffToUrlEvent(t);break;case"uploadJpgImageByIndex":this.isCallbackExist(this.onUploadJpgImageByIndexEvent)&&this.onUploadJpgImageByIndexEvent(t);break;case"upload":this.imageCount=t.imageCount,this.isCallbackExist(this.onUploadEvent)&&this.onUploadEvent(t);break;case"imageEdited":this.isCallbackExist(this.onImageEditedEvent)&&this.onImageEditedEvent(t);break;case"imageDrap":this.isCallbackExist(this.onImageDrapEvent)&&this.onImageDrapEvent(t)}}sendWebSocketCommand(e){try{1===this.h5socket.readyState?this.h5socket.send(JSON.stringify(e)):alert("发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!")}catch(e){alert("发送扫描指令失败！"+e)}}setLicenseKey(e,t,o,s){const n={cmd_type:"setLicenseKey",licenseMode:e,key1:t,key2:o,url:s};this.sendWebSocketCommand(n)}loadDevices(){this.sendWebSocketCommand({cmd_type:"getDevicesList"})}selectScanDevice(e){const t={cmd_type:"selectScanDevice",deviceIndex:e};this.sendWebSocketCommand(t)}startScan(){const e={cmd_type:"startScan",config:this.scaner_work_config};this.sendWebSocketCommand(e)}clearAll(){this.sendWebSocketCommand({cmd_type:"clearAll"})}getImageCount(){this.sendWebSocketCommand({cmd_type:"getImageCount"})}getAllImage(){this.sendWebSocketCommand({cmd_type:"getAllImage"})}getImageById(e){const t={cmd_type:"getImageById",index:e};this.sendWebSocketCommand(t)}loadImageFromUrl(e){const t={cmd_type:"loadImageFromUrl",url:e};this.sendWebSocketCommand(t)}rotateImage(e,t){const o={cmd_type:"rotateImage",index:e,angle:t};this.sendWebSocketCommand(o)}getImageSize(e){const t={cmd_type:"getImageSize",index:e};this.sendWebSocketCommand(t)}deleteImageByIndex(e){const t={cmd_type:"deleteImageByIndex",index:e};this.sendWebSocketCommand(t)}uploadAllImageAsPdfToUrl(e,t,o){const s={cmd_type:"uploadAllImageAsPdfToUrl",url:e,id:t,desc:o};this.sendWebSocketCommand(s)}uploadAllImageAsTiffToUrl(e,t,o){const s={cmd_type:"uploadAllImageAsTiffToUrl",url:e,id:t,desc:o};this.sendWebSocketCommand(s)}uploadJpgImageByIndex(e,t,o,s){const n={cmd_type:"uploadJpgImageByIndex",index:s,url:e,id:t,desc:o};this.sendWebSocketCommand(n)}saveAllImageToLocal(e){const t={cmd_type:"saveAllImageToLocal",filename:e};this.sendWebSocketCommand(t)}openClientLocalfile(){this.sendWebSocketCommand({cmd_type:"openClientLocalfile"})}ftpUploadAllImage(e,t,o,s,n,a){const i={cmd_type:"ftpUploadAllImage",serverIp:e,port:t,username:o,password:s,serverPath:n,filename:a};this.sendWebSocketCommand(i)}setUploadButtonVisible(e){const t={cmd_type:"setUploadButtonVisible",visible:e};this.sendWebSocketCommand(t)}setFocus(){this.sendWebSocketCommand({cmd_type:"focus"})}hidden(){this.sendWebSocketCommand({cmd_type:"hidden"})}closeWebSocket(){this.h5socket.close()}}return"undefined"!=typeof module&&module.exports&&(module.exports=e),"undefined"!=typeof window&&(window.ScanOnWeb=e),e}));
