<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScanOnWeb Vue 3 示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>ScanOnWeb Vue 3 扫描控件示例</h1>
        
        <div :class="['status', statusType]">{{ statusMessage }}</div>
        
        <div>
            <button class="button" @click="loadDevices" :disabled="!isConnected">获取设备列表</button>
            <button class="button" @click="startScan" :disabled="!isConnected">开始扫描</button>
            <button class="button" @click="getImageCount" :disabled="!isConnected">获取图像数量</button>
            <button class="button" @click="getAllImages" :disabled="!isConnected">获取所有图像</button>
            <button class="button" @click="clearAll" :disabled="!isConnected">清除所有图像</button>
        </div>
        
        <div v-if="devices.length > 0">
            <h3>扫描设备列表:</h3>
            <ul>
                <li v-for="(device, index) in devices" :key="index">
                    {{ device.name || `设备 ${index}` }}
                    <button class="button" @click="selectDevice(index)" style="margin-left: 10px; padding: 5px 10px;">选择</button>
                </li>
            </ul>
        </div>
        
        <div v-if="imageCount > 0">
            <h3>图像信息:</h3>
            <p>当前图像数量: {{ imageCount }}</p>
        </div>
        
        <h3>日志输出:</h3>
        <div class="log">
            <div v-for="(logEntry, index) in logs" :key="index">
                [{{ logEntry.time }}] {{ logEntry.message }}
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script type="module">
        import ScanOnWeb from './dist/scanonweb.esm.js';
        
        const { createApp, ref, onMounted } = Vue;
        
        createApp({
            setup() {
                const scanner = ref(null);
                const isConnected = ref(false);
                const statusMessage = ref('正在初始化...');
                const statusType = ref('warning');
                const logs = ref([]);
                const devices = ref([]);
                const imageCount = ref(0);
                
                function log(message) {
                    const timestamp = new Date().toLocaleTimeString();
                    logs.value.push({
                        time: timestamp,
                        message: message
                    });
                    // 保持日志数量在合理范围内
                    if (logs.value.length > 100) {
                        logs.value.shift();
                    }
                }
                
                function updateStatus(message, type = 'success') {
                    statusMessage.value = message;
                    statusType.value = type;
                }
                
                onMounted(() => {
                    try {
                        scanner.value = new ScanOnWeb();
                        log('ScanOnWeb 初始化成功');
                        updateStatus('ScanOnWeb 已初始化，正在尝试连接扫描服务...', 'warning');
                        
                        // 设置事件回调
                        scanner.value.onGetDevicesListEvent = (msg) => {
                            log('获取设备列表回调: ' + JSON.stringify(msg));
                            if (msg.devices && msg.devices.length > 0) {
                                devices.value = msg.devices;
                                updateStatus(`发现 ${msg.devices.length} 个扫描设备`, 'success');
                                isConnected.value = true;
                            } else {
                                updateStatus('未发现扫描设备', 'error');
                            }
                        };
                        
                        scanner.value.onScanFinishedEvent = (msg) => {
                            log('扫描完成回调: ' + JSON.stringify(msg));
                            imageCount.value = msg.imageCount || 0;
                            updateStatus(`扫描完成，共获得 ${msg.imageCount} 张图像`, 'success');
                        };
                        
                        scanner.value.onGetImageCountEvent = (msg) => {
                            log('获取图像数量回调: ' + JSON.stringify(msg));
                            imageCount.value = msg.imageCount || 0;
                            updateStatus(`当前图像数量: ${msg.imageCount}`, 'success');
                        };
                        
                        scanner.value.onGetAllImageEvent = (msg) => {
                            log('获取所有图像回调: ' + JSON.stringify(msg));
                            imageCount.value = msg.imageCount || 0;
                            updateStatus(`获取到 ${msg.imageCount} 张图像`, 'success');
                        };
                        
                        scanner.value.onSelectScanDeviceEvent = (msg) => {
                            log('选择设备回调: ' + JSON.stringify(msg));
                            updateStatus('设备选择成功', 'success');
                        };
                        
                        // 模拟连接成功（实际应该等待 WebSocket 连接成功的回调）
                        setTimeout(() => {
                            if (!isConnected.value) {
                                updateStatus('WebSocket 连接超时，请检查扫描服务是否启动', 'error');
                                log('WebSocket 连接超时');
                            }
                        }, 5000);
                        
                    } catch (error) {
                        log('初始化失败: ' + error.message);
                        updateStatus('初始化失败: ' + error.message, 'error');
                    }
                });
                
                function loadDevices() {
                    if (scanner.value) {
                        log('请求获取设备列表...');
                        scanner.value.loadDevices();
                    }
                }
                
                function startScan() {
                    if (scanner.value) {
                        log('开始扫描...');
                        scanner.value.startScan();
                    }
                }
                
                function getImageCount() {
                    if (scanner.value) {
                        log('请求获取图像数量...');
                        scanner.value.getImageCount();
                    }
                }
                
                function getAllImages() {
                    if (scanner.value) {
                        log('请求获取所有图像...');
                        scanner.value.getAllImage();
                    }
                }
                
                function clearAll() {
                    if (scanner.value) {
                        log('清除所有图像...');
                        scanner.value.clearAll();
                        imageCount.value = 0;
                        updateStatus('已清除所有图像', 'success');
                    }
                }
                
                function selectDevice(index) {
                    if (scanner.value) {
                        log(`选择设备 ${index}...`);
                        scanner.value.selectScanDevice(index);
                    }
                }
                
                return {
                    isConnected,
                    statusMessage,
                    statusType,
                    logs,
                    devices,
                    imageCount,
                    loadDevices,
                    startScan,
                    getImageCount,
                    getAllImages,
                    clearAll,
                    selectDevice
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
