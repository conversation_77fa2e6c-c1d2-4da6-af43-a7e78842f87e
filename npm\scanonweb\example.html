<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScanOnWeb 测试示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .log {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>ScanOnWeb 扫描控件测试</h1>
    
    <div id="status" class="status">等待初始化...</div>
    
    <div>
        <button class="button" onclick="loadDevices()">获取设备列表</button>
        <button class="button" onclick="startScan()">开始扫描</button>
        <button class="button" onclick="getImageCount()">获取图像数量</button>
        <button class="button" onclick="getAllImages()">获取所有图像</button>
        <button class="button" onclick="clearAll()">清除所有图像</button>
    </div>
    
    <h3>日志输出:</h3>
    <div id="log" class="log"></div>
    
    <script src="dist/scanonweb.umd.min.js"></script>
    <script>
        let scanner;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        // 初始化扫描器
        try {
            scanner = new ScanOnWeb();
            log('ScanOnWeb 初始化成功');
            updateStatus('ScanOnWeb 已初始化，正在尝试连接扫描服务...', 'success');
            
            // 设置事件回调
            scanner.onGetDevicesListEvent = function(msg) {
                log('获取设备列表回调: ' + JSON.stringify(msg));
                if (msg.devices && msg.devices.length > 0) {
                    updateStatus(`发现 ${msg.devices.length} 个扫描设备`, 'success');
                } else {
                    updateStatus('未发现扫描设备', 'error');
                }
            };
            
            scanner.onScanFinishedEvent = function(msg) {
                log('扫描完成回调: ' + JSON.stringify(msg));
                updateStatus(`扫描完成，共获得 ${msg.imageCount} 张图像`, 'success');
            };
            
            scanner.onGetImageCountEvent = function(msg) {
                log('获取图像数量回调: ' + JSON.stringify(msg));
                updateStatus(`当前图像数量: ${msg.imageCount}`, 'success');
            };
            
            scanner.onGetAllImageEvent = function(msg) {
                log('获取所有图像回调: ' + JSON.stringify(msg));
                updateStatus(`获取到 ${msg.imageCount} 张图像`, 'success');
            };
            
            scanner.onSelectScanDeviceEvent = function(msg) {
                log('选择设备回调: ' + JSON.stringify(msg));
            };
            
        } catch (error) {
            log('初始化失败: ' + error.message);
            updateStatus('初始化失败: ' + error.message, 'error');
        }
        
        function loadDevices() {
            if (scanner) {
                log('请求获取设备列表...');
                scanner.loadDevices();
            } else {
                log('扫描器未初始化');
                updateStatus('扫描器未初始化', 'error');
            }
        }
        
        function startScan() {
            if (scanner) {
                log('开始扫描...');
                scanner.startScan();
            } else {
                log('扫描器未初始化');
                updateStatus('扫描器未初始化', 'error');
            }
        }
        
        function getImageCount() {
            if (scanner) {
                log('请求获取图像数量...');
                scanner.getImageCount();
            } else {
                log('扫描器未初始化');
                updateStatus('扫描器未初始化', 'error');
            }
        }
        
        function getAllImages() {
            if (scanner) {
                log('请求获取所有图像...');
                scanner.getAllImage();
            } else {
                log('扫描器未初始化');
                updateStatus('扫描器未初始化', 'error');
            }
        }
        
        function clearAll() {
            if (scanner) {
                log('清除所有图像...');
                scanner.clearAll();
                updateStatus('已清除所有图像', 'success');
            } else {
                log('扫描器未初始化');
                updateStatus('扫描器未初始化', 'error');
            }
        }
    </script>
</body>
</html>
