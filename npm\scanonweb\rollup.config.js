import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import babel from "@rollup/plugin-babel";
import terser from "@rollup/plugin-terser";
import { readFileSync } from "fs";

const pkg = JSON.parse(readFileSync("./package.json", "utf8"));

const banner = `/*!
 * ${pkg.name} v${pkg.version}
 * ${pkg.description}
 * ${pkg.homepage}
 * 
 * Copyright (c) ${new Date().getFullYear()} ${pkg.author.name}
 * Licensed under the ${pkg.license} license
 */`;

export default [
  // UMD build for browsers
  {
    input: "src/index.js",
    output: {
      file: "dist/scanonweb.umd.js",
      format: "umd",
      name: "ScanOnWeb",
      banner,
      exports: "default",
    },
    plugins: [
      resolve({
        browser: true,
      }),
      commonjs(),
      babel({
        babelHelpers: "bundled",
        exclude: "node_modules/**",
        presets: [
          [
            "@babel/preset-env",
            {
              targets: {
                browsers: [
                  "> 1%",
                  "last 2 versions",
                  "not dead",
                  "not ie <= 11",
                ],
              },
            },
          ],
        ],
      }),
    ],
  },

  // UMD minified build for browsers
  {
    input: "src/index.js",
    output: {
      file: "dist/scanonweb.umd.min.js",
      format: "umd",
      name: "ScanOnWeb",
      banner,
      exports: "default",
    },
    plugins: [
      resolve({
        browser: true,
      }),
      commonjs(),
      babel({
        babelHelpers: "bundled",
        exclude: "node_modules/**",
        presets: [
          [
            "@babel/preset-env",
            {
              targets: {
                browsers: [
                  "> 1%",
                  "last 2 versions",
                  "not dead",
                  "not ie <= 11",
                ],
              },
            },
          ],
        ],
      }),
      terser({
        format: {
          comments: /^!/,
        },
      }),
    ],
  },

  // ES module build
  {
    input: "src/index.js",
    output: {
      file: "dist/scanonweb.esm.js",
      format: "es",
      banner,
    },
    plugins: [
      resolve(),
      commonjs(),
      babel({
        babelHelpers: "bundled",
        exclude: "node_modules/**",
        presets: [
          [
            "@babel/preset-env",
            {
              targets: {
                node: "14",
              },
              modules: false,
            },
          ],
        ],
      }),
    ],
  },

  // CommonJS build
  {
    input: "src/index.js",
    output: {
      file: "dist/scanonweb.cjs.js",
      format: "cjs",
      banner,
      exports: "default",
    },
    plugins: [
      resolve(),
      commonjs(),
      babel({
        babelHelpers: "bundled",
        exclude: "node_modules/**",
        presets: [
          [
            "@babel/preset-env",
            {
              targets: {
                node: "14",
              },
            },
          ],
        ],
      }),
    ],
  },
];
